#include "XTcp.h"

#include <iostream>
#include <string.h>
#include <thread>

#ifdef WIN32
#include <windows.h>
#define socklen_t int
#else
#include <sys/types.h>
#include <sys/socket.h>
#include <unistd.h>
#include <arpa/inet.h>
#define closesocket close;
#endif


using namespace std;

// 获取WSA错误信息的辅助函数
void PrintWSAError(const char* operation) {
#ifdef WIN32
        int error = WSAGetLastError();
        std::cerr << "WSA错误代码: " << error << std::endl;
        switch(error) {
            case WSAECONNREFUSED:
                std::cerr << "连接被拒绝 - 服务器可能没有在监听该端口" << std::endl;
                break;
            case WSAENETUNREACH:
                std::cerr << "网络不可达" << std::endl;
                break;
            case WSAETIMEDOUT:
                std::cerr << "连接超时" << std::endl;
                break;
            case WSAEHOSTUNREACH:
                std::cerr << "主机不可达" << std::endl;
                break;
            default:
                std::cerr << "其他网络错误" << std::endl;
                break;
        }
#endif
}

XTcp::XTcp(){
#ifdef WIN32
static bool first = true;
if (first)
{
    first = false;
  WSADATA ws;
WSAStartup(MAKEWORD(2,2),&ws);
}
#endif
}

int XTcp::CreateSocket(){
    sock = socket(AF_INET,SOCK_STREAM,0);
    if (sock <= 0)
    {
        cout << "socket create failed"<<endl;
        return -1;
    }
    cout << "socket created: " << sock << endl;
    return sock;
}

bool XTcp::Bind(unsigned short port){
    // 如果socket还没有创建，先创建它
    if (sock <= 0) {
        if (CreateSocket() <= 0) {
            cout << "Failed to create socket for bind" << endl;
            return false;
        }
    }

    sockaddr_in saddr;
    saddr.sin_family = AF_INET;
    saddr.sin_port = htons(port);
    saddr.sin_addr.s_addr = htonl(0);

    if(bind(sock,(sockaddr*)&saddr,sizeof(saddr))!=0){
        cout << "bind port " << port << " failed"<<endl;
        PrintWSAError("bind");
        closesocket(sock);
        sock = 0;
        return false;
    }
    cout << "bind port " << port << " success"<<endl;

    if(listen(sock,10) != 0){
        cout << "listen failed"<<endl;
        closesocket(sock);
        sock = 0;
        return false;
    }

    return true;
}


XTcp XTcp::Accept(){
  XTcp tcp;
  sockaddr_in caddr;
int len = sizeof(caddr);
int client = accept(sock,(sockaddr*)&caddr,&len);
if(client <= 0){
    PrintWSAError("accept");
    return tcp;
}
cout<<"accept client:" << client << endl ;
tcp.ip = inet_ntoa(caddr.sin_addr);
tcp.port = ntohs(caddr.sin_port);
tcp.sock = client;
cout << "ip is "<< tcp.ip.c_str() << "port is "<< tcp.port <<endl;
return tcp;
}

bool XTcp::Connect(const char *ip,unsigned short port){
    if(sock <= 0) {
        if (CreateSocket() <= 0) {
            cout << "Failed to create socket for connect" << endl;
            return false;
        }
    }
   sockaddr_in saddr;
    saddr.sin_family = AF_INET;
    saddr.sin_port = htons(port);
    saddr.sin_addr.s_addr = inet_addr(ip);
    if(connect(sock,(sockaddr*)&saddr,sizeof(saddr))!=0){
        cout <<"connect " <<ip<<":"<< port<<" failed!"<<endl;
        PrintWSAError("connect");
        return false;
    }
 cout <<"connect" <<ip<<":"<< port<<"success!"<<endl;
    return true;
}


void XTcp::Close(){
    if (sock <= 0) return;
    closesocket(sock);
    sock = 0;  // 重置socket值
}

int XTcp::Recv(char *buf,int bufsize){
return recv(sock,buf,bufsize,0);
}

int XTcp::Send(const char *buf,int size){
int s = 0;

while (s != size)
{
  int len = send (sock,buf+s,size - s,0);
  if (len <= 0)break;
  
  s += len;
  
}
return s;
}

XTcp::~XTcp(){}