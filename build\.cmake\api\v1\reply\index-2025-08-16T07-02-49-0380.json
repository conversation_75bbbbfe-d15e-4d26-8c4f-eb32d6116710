{"cmake": {"generator": {"multiConfig": false, "name": "MinGW Makefiles"}, "paths": {"cmake": "D:/C++source/Tools/Cmake/bin/cmake.exe", "cpack": "D:/C++source/Tools/Cmake/bin/cpack.exe", "ctest": "D:/C++source/Tools/Cmake/bin/ctest.exe", "root": "D:/C++source/Tools/Cmake/share/cmake-4.1"}, "version": {"isDirty": false, "major": 4, "minor": 1, "patch": 0, "string": "4.1.0", "suffix": ""}}, "objects": [{"jsonFile": "codemodel-v2-4ffa122f73a178b91815.json", "kind": "codemodel", "version": {"major": 2, "minor": 8}}, {"jsonFile": "cache-v2-2b9ed4c2188534bd7092.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-6954a13f2907b5b78a85.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}, {"jsonFile": "toolchains-v1-2c42b4bb4a240d40c3d1.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}], "reply": {"client-vscode": {"query.json": {"requests": [{"kind": "cache", "version": 2}, {"kind": "codemodel", "version": 2}, {"kind": "toolchains", "version": 1}, {"kind": "cmakeFiles", "version": 1}], "responses": [{"jsonFile": "cache-v2-2b9ed4c2188534bd7092.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "codemodel-v2-4ffa122f73a178b91815.json", "kind": "codemodel", "version": {"major": 2, "minor": 8}}, {"jsonFile": "toolchains-v1-2c42b4bb4a240d40c3d1.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-6954a13f2907b5b78a85.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}]}}}}