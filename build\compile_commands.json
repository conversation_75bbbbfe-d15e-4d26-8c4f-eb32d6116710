[{"directory": "D:/AAVM/Http_Server/build", "command": "D:\\AAVM\\software\\mingw64\\bin\\g++.exe -DWIN32 -DXTCP_EXPORTS -DXTcp_EXPORTS @CMakeFiles/XTcp.dir/includes_CXX.rsp  -Wall -Wextra -g -std=gnu++11 -o CMakeFiles\\XTcp.dir\\Test\\XTcp.cpp.obj -c D:\\AAVM\\Http_Server\\Test\\XTcp.cpp", "file": "D:/AAVM/Http_Server/Test/XTcp.cpp", "output": "CMakeFiles/XTcp.dir/Test/XTcp.cpp.obj"}, {"directory": "D:/AAVM/Http_Server/build", "command": "D:\\AAVM\\software\\mingw64\\bin\\g++.exe -DWIN32 @CMakeFiles/testsocket.dir/includes_CXX.rsp  -Wall -Wextra -g -std=gnu++11 -o CMakeFiles\\testsocket.dir\\Test\\testsocket.cpp.obj -c D:\\AAVM\\Http_Server\\Test\\testsocket.cpp", "file": "D:/AAVM/Http_Server/Test/testsocket.cpp", "output": "CMakeFiles/testsocket.dir/Test/testsocket.cpp.obj"}, {"directory": "D:/AAVM/Http_Server/build", "command": "D:\\AAVM\\software\\mingw64\\bin\\g++.exe -DWIN32 @CMakeFiles/test_server.dir/includes_CXX.rsp  -Wall -Wextra -g -std=gnu++11 -o CMakeFiles\\test_server.dir\\test_server.cpp.obj -c D:\\AAVM\\Http_Server\\test_server.cpp", "file": "D:/AAVM/Http_Server/test_server.cpp", "output": "CMakeFiles/test_server.dir/test_server.cpp.obj"}]