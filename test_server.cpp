// 简单的服务器测试程序，用于验证WSA错误修复
#include <iostream>
#include "Test/XTcp.h"
using namespace std;

int main() {
    cout << "=== XTcp 服务器测试程序 ===" << endl;
    
    XTcp server;
    
    // 测试创建socket
    cout << "1. 创建socket..." << endl;
    int sock_result = server.CreateSocket();
    if (sock_result <= 0) {
        cout << "创建socket失败" << endl;
        return -1;
    }
    cout << "创建socket成功，socket ID: " << sock_result << endl;
    
    // 测试绑定端口
    cout << "2. 绑定端口8080..." << endl;
    if (!server.Bind(8080)) {
        cout << "绑定端口失败" << endl;
        server.Close();
        return -1;
    }
    cout << "绑定端口成功" << endl;
    
    cout << "3. 等待客户端连接..." << endl;
    cout << "可以使用telnet 127.0.0.1 8080 进行测试" << endl;
    cout << "按Ctrl+C退出程序" << endl;
    
    // 接受一个连接进行测试
    XTcp client = server.Accept();
    if (client.sock > 0) {
        cout << "客户端连接成功！" << endl;
        
        // 发送欢迎消息
        const char* welcome = "Welcome to XTcp Server!\n";
        int sent = client.Send(welcome, strlen(welcome));
        cout << "发送了 " << sent << " 字节数据" << endl;
        
        // 接收数据
        char buffer[1024] = {0};
        int received = client.Recv(buffer, sizeof(buffer)-1);
        if (received > 0) {
            buffer[received] = '\0';
            cout << "收到客户端数据: " << buffer << endl;
        }
        
        client.Close();
    }
    
    server.Close();
    cout << "服务器关闭" << endl;
    
    return 0;
}
