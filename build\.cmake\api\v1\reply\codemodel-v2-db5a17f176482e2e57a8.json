{"configurations": [{"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-Debug-6c7bb0ac8d6ff1516542.json", "minimumCMakeVersion": {"string": "3.10"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "Http_Server", "targetIndexes": [0, 1, 2]}], "targets": [{"directoryIndex": 0, "id": "XTcp::@6890427a1f51a3e7e1df", "jsonFile": "target-XTcp-Debug-68379fa77f0933fee148.json", "name": "XTcp", "projectIndex": 0}, {"directoryIndex": 0, "id": "fixed_client::@6890427a1f51a3e7e1df", "jsonFile": "target-fixed_client-Debug-26b97429328edc674652.json", "name": "fixed_client", "projectIndex": 0}, {"directoryIndex": 0, "id": "testsocket::@6890427a1f51a3e7e1df", "jsonFile": "target-testsocket-Debug-9a7d702294d59a07b6a6.json", "name": "testsocket", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "D:/AAVM/Http_Server/build", "source": "D:/AAVM/Http_Server"}, "version": {"major": 2, "minor": 8}}