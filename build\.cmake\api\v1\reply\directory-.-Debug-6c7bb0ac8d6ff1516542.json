{"backtraceGraph": {"commands": ["install"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 84, "parent": 0}, {"command": 0, "file": 0, "line": 90, "parent": 0}]}, "installers": [{"backtrace": 1, "component": "Unspecified", "destination": "bin", "paths": ["bin/testsocket.exe"], "targetId": "testsocket::@6890427a1f51a3e7e1df", "targetIndex": 2, "type": "target"}, {"backtrace": 1, "component": "Unspecified", "destination": "bin", "paths": ["bin/fixed_client.exe"], "targetId": "fixed_client::@6890427a1f51a3e7e1df", "targetIndex": 1, "type": "target"}, {"backtrace": 1, "component": "Unspecified", "destination": "lib", "paths": ["lib/libXTcp.dll.a"], "targetId": "XTcp::@6890427a1f51a3e7e1df", "targetIndex": 0, "targetIsImportLibrary": true, "type": "target"}, {"backtrace": 1, "component": "Unspecified", "destination": "bin", "paths": ["bin/libXTcp.dll"], "targetId": "XTcp::@6890427a1f51a3e7e1df", "targetIndex": 0, "type": "target"}, {"backtrace": 2, "component": "Unspecified", "destination": "include", "paths": ["Test/XTcp.h"], "type": "file"}], "paths": {"build": ".", "source": "."}}