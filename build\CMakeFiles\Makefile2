# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 4.1

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = D:\C++source\Tools\Cmake\bin\cmake.exe

# The command to remove a file.
RM = D:\C++source\Tools\Cmake\bin\cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = D:\AAVM\Http_Server

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = D:\AAVM\Http_Server\build

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/XTcp.dir/all
all: CMakeFiles/testsocket.dir/all
all: CMakeFiles/fixed_client.dir/all
.PHONY : all

# The main recursive "codegen" target.
codegen: CMakeFiles/XTcp.dir/codegen
codegen: CMakeFiles/testsocket.dir/codegen
codegen: CMakeFiles/fixed_client.dir/codegen
.PHONY : codegen

# The main recursive "preinstall" target.
preinstall:
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/XTcp.dir/clean
clean: CMakeFiles/testsocket.dir/clean
clean: CMakeFiles/fixed_client.dir/clean
.PHONY : clean

#=============================================================================
# Target rules for target CMakeFiles/XTcp.dir

# All Build rule for target.
CMakeFiles/XTcp.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\XTcp.dir\build.make CMakeFiles/XTcp.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles\XTcp.dir\build.make CMakeFiles/XTcp.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=D:\AAVM\Http_Server\build\CMakeFiles --progress-num=1,2 "Built target XTcp"
.PHONY : CMakeFiles/XTcp.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/XTcp.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start D:\AAVM\Http_Server\build\CMakeFiles 2
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 CMakeFiles/XTcp.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start D:\AAVM\Http_Server\build\CMakeFiles 0
.PHONY : CMakeFiles/XTcp.dir/rule

# Convenience name for target.
XTcp: CMakeFiles/XTcp.dir/rule
.PHONY : XTcp

# codegen rule for target.
CMakeFiles/XTcp.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\XTcp.dir\build.make CMakeFiles/XTcp.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=D:\AAVM\Http_Server\build\CMakeFiles --progress-num=1,2 "Finished codegen for target XTcp"
.PHONY : CMakeFiles/XTcp.dir/codegen

# clean rule for target.
CMakeFiles/XTcp.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\XTcp.dir\build.make CMakeFiles/XTcp.dir/clean
.PHONY : CMakeFiles/XTcp.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/testsocket.dir

# All Build rule for target.
CMakeFiles/testsocket.dir/all: CMakeFiles/XTcp.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles\testsocket.dir\build.make CMakeFiles/testsocket.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles\testsocket.dir\build.make CMakeFiles/testsocket.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=D:\AAVM\Http_Server\build\CMakeFiles --progress-num=5,6 "Built target testsocket"
.PHONY : CMakeFiles/testsocket.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/testsocket.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start D:\AAVM\Http_Server\build\CMakeFiles 4
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 CMakeFiles/testsocket.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start D:\AAVM\Http_Server\build\CMakeFiles 0
.PHONY : CMakeFiles/testsocket.dir/rule

# Convenience name for target.
testsocket: CMakeFiles/testsocket.dir/rule
.PHONY : testsocket

# codegen rule for target.
CMakeFiles/testsocket.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\testsocket.dir\build.make CMakeFiles/testsocket.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=D:\AAVM\Http_Server\build\CMakeFiles --progress-num=5,6 "Finished codegen for target testsocket"
.PHONY : CMakeFiles/testsocket.dir/codegen

# clean rule for target.
CMakeFiles/testsocket.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\testsocket.dir\build.make CMakeFiles/testsocket.dir/clean
.PHONY : CMakeFiles/testsocket.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/fixed_client.dir

# All Build rule for target.
CMakeFiles/fixed_client.dir/all: CMakeFiles/XTcp.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles\fixed_client.dir\build.make CMakeFiles/fixed_client.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles\fixed_client.dir\build.make CMakeFiles/fixed_client.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=D:\AAVM\Http_Server\build\CMakeFiles --progress-num=3,4 "Built target fixed_client"
.PHONY : CMakeFiles/fixed_client.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/fixed_client.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start D:\AAVM\Http_Server\build\CMakeFiles 4
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 CMakeFiles/fixed_client.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start D:\AAVM\Http_Server\build\CMakeFiles 0
.PHONY : CMakeFiles/fixed_client.dir/rule

# Convenience name for target.
fixed_client: CMakeFiles/fixed_client.dir/rule
.PHONY : fixed_client

# codegen rule for target.
CMakeFiles/fixed_client.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\fixed_client.dir\build.make CMakeFiles/fixed_client.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=D:\AAVM\Http_Server\build\CMakeFiles --progress-num=3,4 "Finished codegen for target fixed_client"
.PHONY : CMakeFiles/fixed_client.dir/codegen

# clean rule for target.
CMakeFiles/fixed_client.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\fixed_client.dir\build.make CMakeFiles/fixed_client.dir/clean
.PHONY : CMakeFiles/fixed_client.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 0
.PHONY : cmake_check_build_system

