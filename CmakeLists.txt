cmake_minimum_required(VERSION 3.10)

# 项目名称
project(Http_Server)

# 设置 C++ 标准
set(CMAKE_CXX_STANDARD 11)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 设置编译器标志
if(WIN32)
    # Windows 特定设置
    add_definitions(-DWIN32)
endif()

# 根据编译器类型设置标志
if(MSVC)
    # MSVC 编译器
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} /W3")
else()
    # GCC/Clang 编译器 (包括 MinGW)
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -Wextra")
endif()

# 查找线程库
find_package(Threads REQUIRED)

# 包含目录
include_directories(Test)

# XTcp 库
add_library(XTcp SHARED
    Test/XTcp.cpp
    Test/XTcp.h
)

# 设置DLL导出宏
target_compile_definitions(XTcp PRIVATE XTCP_EXPORTS)

# XTcp 库链接设置
target_link_libraries(XTcp Threads::Threads)
if(WIN32)
    target_link_libraries(XTcp ws2_32)
endif()

# testsocket 可执行文件
add_executable(testsocket
    Test/testsocket.cpp
)

# testsocket 链接库
target_link_libraries(testsocket XTcp Threads::Threads)
if(WIN32)
    target_link_libraries(testsocket ws2_32)
endif()

# test_server 测试程序
if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/test_server.cpp)
    add_executable(test_server
        test_server.cpp
    )

    target_link_libraries(test_server XTcp Threads::Threads)
    if(WIN32)
        target_link_libraries(test_server ws2_32)
    endif()

    set_target_properties(test_server PROPERTIES
        RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
    )
endif()

# 设置输出目录
set_target_properties(testsocket PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)

set_target_properties(XTcp PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
    ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib
    LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib
)

# 安装规则
install(TARGETS testsocket XTcp
    RUNTIME DESTINATION bin
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
)

install(FILES Test/XTcp.h
    DESTINATION include
)