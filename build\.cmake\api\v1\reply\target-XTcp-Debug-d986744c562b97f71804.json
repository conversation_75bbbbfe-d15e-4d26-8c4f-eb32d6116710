{"artifacts": [{"path": "bin/libXTcp.dll"}, {"path": "lib/libXTcp.dll.a"}, {"path": "bin/libXTcp.pdb"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "install", "target_link_libraries", "set_property", "find_package", "add_definitions", "target_compile_definitions", "include_directories"], "files": ["CMakeLists.txt", "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/FindThreads.cmake"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 32, "parent": 0}, {"command": 1, "file": 0, "line": 85, "parent": 0}, {"command": 2, "file": 0, "line": 43, "parent": 0}, {"command": 4, "file": 0, "line": 26, "parent": 0}, {"file": 1, "parent": 4}, {"command": 3, "file": 1, "line": 238, "parent": 5}, {"command": 5, "file": 0, "line": 13, "parent": 0}, {"command": 6, "file": 0, "line": 38, "parent": 0}, {"command": 7, "file": 0, "line": 29, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": " -Wall -Wextra -g -std=gnu++11"}], "defines": [{"backtrace": 7, "define": "WIN32"}, {"backtrace": 8, "define": "XTCP_EXPORTS"}, {"define": "XTcp_EXPORTS"}], "includes": [{"backtrace": 9, "path": "D:/AAVM/Http_Server/Test"}], "language": "CXX", "languageStandard": {"backtraces": [1], "standard": "11"}, "sourceIndexes": [0]}], "id": "XTcp::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 2, "path": "lib"}, {"backtrace": 2, "path": "bin"}], "prefix": {"path": "C:/Program Files (x86)/Project"}}, "link": {"commandFragments": [{"fragment": "-shared", "role": "flags"}, {"backtrace": 3, "fragment": "-lws2_32", "role": "libraries"}, {"backtrace": 6, "fragment": "-l<PERSON><PERSON><PERSON>", "role": "libraries"}, {"fragment": "-lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32", "role": "libraries"}], "language": "CXX"}, "name": "XTcp", "nameOnDisk": "libXTcp.dll", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [1]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "Test/XTcp.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Test/XTcp.h", "sourceGroupIndex": 1}], "type": "SHARED_LIBRARY"}