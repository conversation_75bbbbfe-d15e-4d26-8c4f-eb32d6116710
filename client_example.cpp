// 客户端使用XTcp.dll的示例代码
#include <iostream>
#include "Test/XTcp.h"
using namespace std;

int main() {
    // 创建XTcp客户端对象
    XTcp client;
    
    // 创建socket
    if (client.CreateSocket() <= 0) {
        cout << "创建socket失败" << endl;
        return -1;
    }
    
    // 这里可以添加连接到服务器的代码
    // 例如：client.Connect("127.0.0.1", 8080);
    
    cout << "XTcp客户端示例运行成功" << endl;
    
    // 关闭连接
    client.Close();
    
    return 0;
}
