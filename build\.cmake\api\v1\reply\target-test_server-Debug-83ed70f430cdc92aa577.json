{"artifacts": [{"path": "bin/test_server.exe"}, {"path": "bin/test_server.pdb"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "target_link_libraries", "set_property", "find_package", "add_definitions", "include_directories"], "files": ["CMakeLists.txt", "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/FindThreads.cmake"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 59, "parent": 0}, {"command": 1, "file": 0, "line": 63, "parent": 0}, {"command": 1, "file": 0, "line": 65, "parent": 0}, {"command": 3, "file": 0, "line": 26, "parent": 0}, {"file": 1, "parent": 4}, {"command": 2, "file": 1, "line": 238, "parent": 5}, {"command": 4, "file": 0, "line": 13, "parent": 0}, {"command": 5, "file": 0, "line": 29, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": " -Wall -Wextra -g -std=gnu++11"}], "defines": [{"backtrace": 7, "define": "WIN32"}], "includes": [{"backtrace": 8, "path": "D:/AAVM/Http_Server/Test"}], "language": "CXX", "languageStandard": {"backtraces": [1], "standard": "11"}, "sourceIndexes": [0]}], "dependencies": [{"backtrace": 2, "id": "XTcp::@6890427a1f51a3e7e1df"}], "id": "test_server::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "-Wall -Wextra -g", "role": "flags"}, {"backtrace": 2, "fragment": "lib\\libXTcp.dll.a", "role": "libraries"}, {"backtrace": 3, "fragment": "-lws2_32", "role": "libraries"}, {"backtrace": 6, "fragment": "-l<PERSON><PERSON><PERSON>", "role": "libraries"}, {"fragment": "-lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32", "role": "libraries"}], "language": "CXX"}, "name": "test_server", "nameOnDisk": "test_server.exe", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "test_server.cpp", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}