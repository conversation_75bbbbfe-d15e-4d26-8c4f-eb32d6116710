{"configurations": [{"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-Debug-3108120fd1aec6740536.json", "minimumCMakeVersion": {"string": "3.10"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "Http_Server", "targetIndexes": [0, 1, 2]}], "targets": [{"directoryIndex": 0, "id": "XTcp::@6890427a1f51a3e7e1df", "jsonFile": "target-XTcp-Debug-d986744c562b97f71804.json", "name": "XTcp", "projectIndex": 0}, {"directoryIndex": 0, "id": "test_server::@6890427a1f51a3e7e1df", "jsonFile": "target-test_server-Debug-83ed70f430cdc92aa577.json", "name": "test_server", "projectIndex": 0}, {"directoryIndex": 0, "id": "testsocket::@6890427a1f51a3e7e1df", "jsonFile": "target-testsocket-Debug-875024a2d2530b7c14b8.json", "name": "testsocket", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "D:/AAVM/Http_Server/build", "source": "D:/AAVM/Http_Server"}, "version": {"major": 2, "minor": 8}}